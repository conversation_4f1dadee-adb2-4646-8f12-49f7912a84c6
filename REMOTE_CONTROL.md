# 远程控制功能说明

## 概述

远程控制功能允许通过在FTP服务器上放置控制文件来远程控制后台运行的下载脚本。

## 配置

在 `config.yaml` 文件中配置远程控制选项：

```yaml
# 远程控制配置
remote_control:
  enabled: true                                    # 远程控制开关
  communication_path: "remote-control/commands"    # 远程控制通信路径
```

## 真实远程控制通信路径

程序会将配置文件中的 `communication_path` 与基础FTP路径组合：

```
*****************************************//data/CCSPL-SmartCompute/data-out/[communication_path]
```

例如，如果配置中的 `communication_path` 为 `remote-control/commands`，则真实路径为：
```
*****************************************//data/CCSPL-SmartCompute/data-out/remote-control/commands
```

## 控制文件格式

控制文件必须遵循以下命名规则：
```
control[时间戳].yaml
```

其中时间戳格式为：`YYYYMMDDHHMMSS`

例如：`control20241210143000.yaml`

### 控制文件内容

```yaml
# 时间戳 (格式: YYYYMMDDHHMMSS)
timestamp: "20241210143000"

# 要处理的文件名 (可选，如果为空则使用最新文件)
file_name: ""

# 远程服务器地址 (必须是有效的IP地址)
server_ip: "*************"

# 是否退出后台模式 (true: 退出后台模式, false: 继续运行)
exit_daemon: false
```

## 控制命令类型

### 1. 执行下载和部署
- 设置 `server_ip` 为目标服务器IP地址
- 设置 `exit_daemon: false`
- `file_name` 可以为空（使用最新文件）

### 2. 退出后台模式
- 设置 `exit_daemon: true`
- 其他字段可以为空

## 工作流程

1. 程序在后台模式下运行时，每分钟检查一次远程控制通信路径
2. 如果发现新的控制文件，程序会下载并解析该文件
3. 根据控制文件内容执行相应操作：
   - 如果 `exit_daemon: true`，程序退出后台模式
   - 如果指定了 `server_ip`，程序执行下载和部署操作到指定服务器
4. 处理完成后，程序继续监控新的控制文件

## 使用示例

### 示例1：部署到特定服务器
创建文件 `control20241210143000.yaml`：
```yaml
timestamp: "20241210143000"
file_name: ""
server_ip: "*************"
exit_daemon: false
```

### 示例2：退出后台模式
创建文件 `control20241210144500.yaml`：
```yaml
timestamp: "20241210144500"
file_name: ""
server_ip: ""
exit_daemon: true
```

## 启用远程控制功能

要启用远程控制功能，需要：

1. 在 `config.yaml` 中设置 `remote_control.enabled: true`
2. 使用后台模式运行程序：`download_script.exe -d`

示例命令：
```bash
# 启动后台模式，启用远程控制
download_script.exe -d

# 启动后台模式，本地模式，启用远程控制
download_script.exe -d --local
```

## 日志输出

当远程控制功能启用时，程序会在日志中显示：
```
Remote control enabled, communication path: remote-control/commands
Checking remote control files at: *****************************************//data/CCSPL-SmartCompute/data-out/remote-control/commands
```

当发现控制文件时：
```
Found control file: control20241210143000.yaml
Control file parsed successfully: {Timestamp:20241210143000 FileName: ServerIP:************* ExitDaemon:false}
Processing remote control command: {Timestamp:20241210143000 FileName: ServerIP:************* ExitDaemon:false}
```

## 注意事项

1. 控制文件的时间戳必须是有效的日期时间格式
2. 服务器IP地址必须是有效的IPv4地址格式
3. 程序会自动选择时间戳最新的控制文件进行处理
4. 处理完成后，临时下载的控制文件会被自动清理
5. 远程控制功能只在后台模式（daemon mode）下生效
6. 远程控制检查与文件监控同步进行，每分钟检查一次
7. 如果远程控制通信路径不存在或无法访问，程序会记录错误但继续正常运行
