package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-toast/toast"

	"runtime"

	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"
)

// 全局变量用于存储命令行参数

var (
	serverIP   string
	localMode  bool
	daemonMode bool
)

// 计算当月第几周

func getWeekOfMonth(t time.Time) int {
	_, week := t.ISOWeek()
	currentMonth := t.Month()
	firstDay := time.Date(t.Year(), currentMonth, 1, 0, 0, 0, 0, t.Location())
	_, firstWeek := firstDay.ISOWeek()
	return week - firstWeek + 1
}

// 生成目标文件夹名
type RemoteControlConfig struct {
	Enabled           bool   `yaml:"enabled"`
	CommunicationPath string `yaml:"communication_path"`
}

type Config struct {
	ParentDir     string              `yaml:"parent_dir"`
	RemoteControl RemoteControlConfig `yaml:"remote_control"`
}

// 控制文件结构
type ControlFile struct {
	Timestamp  string `yaml:"timestamp"`
	FileName   string `yaml:"file_name"`
	ServerIP   string `yaml:"server_ip"`
	ExitDaemon bool   `yaml:"exit_daemon"`
}

var config Config

func loadConfig(path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}
	return yaml.Unmarshal(data, &config)
}
func generateFolderName(t time.Time) string {
	weekNum := getWeekOfMonth(t)
	return fmt.Sprintf("%d%02dW%d", t.Year(), t.Month(), weekNum)
}

// 验证IP地址格式
func isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
	}
	return true
}

// 检查远程控制文件
func checkRemoteControlFiles() (*ControlFile, error) {
	if !config.RemoteControl.Enabled {
		return nil, nil
	}

	// 构建真实远程控制通信路径
	realPath := fmt.Sprintf("*****************************************//data/CCSPL-SmartCompute/data-out/%s", config.RemoteControl.CommunicationPath)

	log.Printf("Checking remote control files at: %s", realPath)

	// 使用winscp列出远程控制目录下的文件
	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		fmt.Sprintf("cd /data/CCSPL-SmartCompute/data-out/%s", config.RemoteControl.CommunicationPath),
		"ls",
		"exit")

	output, cmdErr := cmd.CombinedOutput()
	if cmdErr != nil {
		log.Printf("Failed to list remote control files: %v\nOutput: %s", cmdErr, output)
		return nil, fmt.Errorf("failed to list remote control files: %v", cmdErr)
	}

	// 解析输出找到最新的控制文件
	files := strings.Split(string(output), "\n")
	var latestTime time.Time
	var latestControlFile string

	// 匹配控制文件格式：control+时间戳+.yaml
	re := regexp.MustCompile(`control(\d{14})\.yaml`)

	for _, file := range files {
		if matches := re.FindStringSubmatch(file); len(matches) > 1 {
			timeStr := matches[1]
			fileTime, err := time.ParseInLocation("20060102150405", timeStr, time.Local)
			if err != nil {
				continue
			}

			if latestTime.IsZero() || fileTime.After(latestTime) {
				latestTime = fileTime
				latestControlFile = matches[0]
			}
		}
	}

	if latestControlFile == "" {
		// 没有找到控制文件，返回nil
		return nil, nil
	}

	log.Printf("Found control file: %s", latestControlFile)

	// 下载控制文件到临时目录
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, latestControlFile)

	downloadCmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		fmt.Sprintf("cd /data/CCSPL-SmartCompute/data-out/%s", config.RemoteControl.CommunicationPath),
		fmt.Sprintf("get %s %s", latestControlFile, tempFilePath),
		"exit")

	if output, cmdErr := downloadCmd.CombinedOutput(); cmdErr != nil {
		log.Printf("Failed to download control file: %v\nOutput: %s", cmdErr, output)
		return nil, fmt.Errorf("failed to download control file: %v", cmdErr)
	}

	// 读取并解析控制文件
	data, err := os.ReadFile(tempFilePath)
	if err != nil {
		log.Printf("Failed to read control file: %v", err)
		return nil, fmt.Errorf("failed to read control file: %v", err)
	}

	var controlFile ControlFile
	if err := yaml.Unmarshal(data, &controlFile); err != nil {
		log.Printf("Failed to parse control file: %v", err)
		return nil, fmt.Errorf("failed to parse control file: %v", err)
	}

	// 清理临时文件
	os.Remove(tempFilePath)

	log.Printf("Control file parsed successfully: %+v", controlFile)
	return &controlFile, nil
}

// 处理远程控制命令
func processRemoteControl(controlFile *ControlFile) (bool, error) {
	if controlFile == nil {
		return false, nil
	}

	log.Printf("Processing remote control command: %+v", controlFile)

	// 检查是否需要退出后台模式
	if controlFile.ExitDaemon {
		log.Println("Remote control command: Exit daemon mode")
		return true, nil
	}

	// 如果指定了文件名和服务器IP，执行特定的下载和部署操作
	if controlFile.FileName != "" && controlFile.ServerIP != "" {
		log.Printf("Remote control command: Process file %s to server %s", controlFile.FileName, controlFile.ServerIP)

		// 验证服务器IP格式
		if !isValidIP(controlFile.ServerIP) {
			return false, fmt.Errorf("invalid server IP in control file: %s", controlFile.ServerIP)
		}

		// 执行下载和部署操作
		_, err := executeDownloadProcess(controlFile.ServerIP, true)
		if err != nil {
			log.Printf("Failed to process remote control command: %v", err)
			return false, err
		}

		log.Printf("Successfully processed remote control command")
	}

	return false, nil
}

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "download_script [ip_address]",
	Short: "Download and deploy plugin files to remote servers",
	Long: `A CLI tool for downloading plugin files from FTP and deploying them to remote servers.

Examples:
  download_script                           - Use default values
  download_script *************            - Set server IP
  download_script --local                   - Disable remote operations (local mode)
  download_script --local *************    - Local mode (IP ignored)
  download_script -d                        - Enable daemon mode
  download_script -d --local                - Daemon mode with local operations only
  download_script -d *************         - Daemon mode with custom server IP`,
	Args: cobra.MaximumNArgs(1),
	PreRunE: func(cmd *cobra.Command, args []string) error {
		// 设置默认IP
		if serverIP == "" {
			serverIP = "************"
		}

		// 如果提供了位置参数（IP地址），使用它
		if len(args) == 1 {
			if localMode {
				log.Printf("Local mode enabled: IP argument '%s' ignored", args[0])
			} else {
				if !isValidIP(args[0]) {
					return fmt.Errorf("invalid argument '%s'. Expected IP address (x.x.x.x)", args[0])
				}
				serverIP = args[0]
			}
		}

		// 如果启用本地模式，记录日志
		if localMode {
			log.Printf("Local mode enabled: remote operations disabled")
		}

		return nil
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		return runMainLogic()
	},
}

// init函数用于设置命令行标志
func init() {
	rootCmd.Flags().StringVarP(&serverIP, "server", "s", "", "Server IP address (default: ************)")
	rootCmd.Flags().BoolVar(&localMode, "local", false, "Disable remote operations (local mode)")
	rootCmd.Flags().BoolVarP(&daemonMode, "daemon", "d", false, "Enable daemon mode")
}

// 查找FTP目录下的最新文件
func findLatestFile() (latestFile string, err error) {

	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		"cd /data/CCSPL-SmartCompute/data-out/xKF7490/developmentVersion/",
		"ls",
		"exit")

	output, cmdErr := cmd.CombinedOutput()
	if cmdErr != nil {
		log.Printf("Failed to list files: %v\nOutput: %s", cmdErr, output)
		return "", fmt.Errorf("failed to list files: %v", cmdErr)
	}

	// 解析输出找到最新文件
	files := strings.Split(string(output), "\n")
	var latestTime time.Time

	re := regexp.MustCompile(`plugins(\d{14})\.zip`)

	for _, file := range files {
		if matches := re.FindStringSubmatch(file); len(matches) > 1 {
			timeStr := matches[1]
			fileTime, err := time.ParseInLocation("20060102150405", timeStr, time.Local)
			if err != nil {
				continue
			}

			if latestTime.IsZero() || fileTime.After(latestTime) {
				latestTime = fileTime
				latestFile = matches[0]
			}
		}
	}

	if latestFile != "" {
		return latestFile, nil
	} else {
		log.Printf("No matching plugin files found in remote directory")
		return "", fmt.Errorf("no matching plugin files found in remote directory")
	}
}

// 查找本地目录下的最新文件
func findLatestFileLocal() (latestFile string, err error) {
	// 使用与executeDownloadProcess相同的逻辑确定文件夹路径
	now := time.Now()
	folderName := generateFolderName(now)
	targetDir := filepath.Join(config.ParentDir, folderName)

	// 检查目录是否存在
	if _, statErr := os.Stat(targetDir); os.IsNotExist(statErr) {
		// 目录不存在，返回空字符串和nil错误
		return "", nil
	}

	// 读取目录中的文件
	files, readErr := os.ReadDir(targetDir)
	if readErr != nil {
		// 目录存在但无法读取，返回空字符串和错误
		return "", readErr
	}

	// 使用与findLatestFile相同的文件匹配逻辑
	var latestTime time.Time
	re := regexp.MustCompile(`plugins(\d{14})\.zip`)

	for _, file := range files {
		if file.IsDir() {
			continue // 跳过目录
		}

		fileName := file.Name()
		if matches := re.FindStringSubmatch(fileName); len(matches) > 1 {
			timeStr := matches[1]
			fileTime, parseErr := time.ParseInLocation("20060102150405", timeStr, time.Local)
			if parseErr != nil {
				continue
			}

			if latestTime.IsZero() || fileTime.After(latestTime) {
				latestTime = fileTime
				latestFile = matches[0]
			}
		}
	}

	// 如果找到文件返回文件名，否则返回空字符串和nil错误
	return latestFile, nil
}

// 执行下载和上传的核心业务逻辑
func executeDownloadProcess(serverIP string, remote_enable bool) (latestFile string, err error) {
	if remote_enable {
		// 远程到服务器，停止服务
		log.Printf("Stopping remote service on server %s...", serverIP)

		// 使用单行命令执行远程服务停止
		cmd := exec.Command("winscp.com", "/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call systemctl stop fist",
			"exit")

		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to execute command: %v\nOutput: %s", cmdErr, output)
			return "", fmt.Errorf("failed to stop remote service: %v", cmdErr)
		}

		log.Println("Remote service stopped.")
	}

	// 查找ftp目录下所有文件并找出最新的
	log.Println("Searching for latest file...")
	latestFile, err = findLatestFile()
	if err != nil {
		return "", err
	}
	log.Printf("Found latest file: %s", latestFile)

	// 创建目标文件夹
	now := time.Now()
	folderName := generateFolderName(now)
	targetDir := filepath.Join(config.ParentDir, folderName)

	if mkdirErr := os.MkdirAll(targetDir, 0755); mkdirErr != nil {
		log.Printf("Failed to create directory: %v", mkdirErr)
		return latestFile, fmt.Errorf("failed to create directory: %v", mkdirErr)
	}

	log.Printf("Downloading to folder: %s", targetDir)

	// 下载文件
	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		"cd /data/CCSPL-SmartCompute/data-out/xKF7490/developmentVersion/",
		fmt.Sprintf("get %s %s\\%s", latestFile, targetDir, latestFile),
		"exit")

	if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
		log.Printf("Failed to download file: %v\nOutput: %s", cmdErr, output)
		return latestFile, fmt.Errorf("failed to download file: %v", cmdErr)
	}

	log.Printf("Successfully downloaded %s to %s", latestFile, targetDir)

	// 解压文件到下载目录
	log.Println("Extracting file...")

	// 构建源文件完整路径
	zipPath := filepath.Join(targetDir, latestFile)

	// 使用7zip解压文件，-y表示对所有询问自动回答yes（覆盖）
	cmd = exec.Command("7z", "x", zipPath, "-o"+targetDir, "-y")

	if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
		log.Printf("Failed to extract file: %v\nOutput: %s", cmdErr, output)
		return latestFile, fmt.Errorf("failed to extract file: %v", cmdErr)
	}

	log.Printf("Successfully extracted %s to %s", latestFile, targetDir)

	if remote_enable {
		// 两阶段远程部署：上传zip文件到服务器，然后远程解压和部署
		log.Printf("Starting two-stage remote deployment to server %s...", serverIP)

		// 构建zip文件路径
		now := time.Now()
		folderName := generateFolderName(now)
		targetDir := filepath.Join(config.ParentDir, folderName)
		zipPath := filepath.Join(targetDir, latestFile)

		// 检查zip文件是否存在
		if _, statErr := os.Stat(zipPath); os.IsNotExist(statErr) {
			log.Printf("Zip file not found at: %s", zipPath)
			return latestFile, fmt.Errorf("zip file not found at: %s", zipPath)
		}

		// 第一阶段：上传zip文件到远程服务器的/root目录
		log.Printf("Stage 1: Uploading zip file %s to /root directory...", latestFile)
		uploadCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			fmt.Sprintf("put %s /root/", zipPath),
			"exit"}
		cmd := exec.Command("winscp.com", uploadCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to upload zip file: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to upload zip file: %v", cmdErr)
		}
		log.Printf("Successfully uploaded %s to /root/", latestFile)

		// 第二阶段：远程解压和部署
		log.Printf("Stage 2: Remote extraction and deployment...")

		// 远程命令：解压zip文件到/root目录
		extractCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			fmt.Sprintf("call cd /root && unzip -o %s", latestFile),
			"exit"}
		cmd = exec.Command("winscp.com", extractCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to extract zip file remotely: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to extract zip file remotely: %v", cmdErr)
		}
		log.Println("Successfully extracted zip file on remote server")

		// 远程命令：移动plugins文件夹到目标位置（覆盖现有文件）
		deployCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call cp -rf /root/plugins/* /usr/local/appfist/AppFist_now/plugins/",
			"exit"}
		cmd = exec.Command("winscp.com", deployCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to deploy plugins folder: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to deploy plugins folder: %v", cmdErr)
		}
		log.Println("Successfully deployed plugins folder to target location")

		// 远程命令：清理临时文件
		cleanupCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			fmt.Sprintf("call rm -f /root/%s && rm -rf /root/plugins", latestFile),
			"exit"}
		cmd = exec.Command("winscp.com", cleanupCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Warning: Failed to cleanup temporary files: %v\nOutput: %s", cmdErr, output)
			// 清理失败不影响主流程，只记录警告
		} else {
			log.Println("Successfully cleaned up temporary files")
		}

		// 执行启动服务命令
		log.Printf("Restarting service...")
		startCommand := []string{"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call systemctl restart fist",
			"exit"}

		cmd = exec.Command("winscp.com", startCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to start service: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to start service: %v", cmdErr)
		}
		log.Println("Successfully restarted service")
		log.Println("Two-stage remote deployment completed successfully")
	}

	// 所有操作成功完成
	return latestFile, nil
}

// runMainLogic 包含原main函数的核心业务逻辑
func runMainLogic() error {
	// 计算remote_enable和daemon_enable的值
	remote_enable := !localMode
	daemon_enable := daemonMode

	// 显示当前配置
	log.Printf("Configuration: serverIP=%s, remote_enable=%t, daemon_enable=%t", serverIP, remote_enable, daemon_enable)

	// 查找本地最新的文件作为 latestFile
	var latestFile string
	var err error
	if daemon_enable {
		latestFile, err = findLatestFileLocal()
		if err != nil {
			log.Printf("Failed to find latest file locally: %v", err)
			os.Exit(1)
		}
		log.Printf("Latest file found locally: %s", latestFile)
	} else {
		latestFile = ""
	}

	var processErr error

	// 后台模式
	if daemon_enable {
		log.Println("Entering daemon mode...")
		if config.RemoteControl.Enabled {
			log.Printf("Remote control enabled, communication path: %s", config.RemoteControl.CommunicationPath)
		}
	}

	// 定时查询最新的文件，如果有变更就执行核心业务逻辑
	for {
		// 在后台模式下检查远程控制文件
		if daemon_enable && config.RemoteControl.Enabled {
			controlFile, err := checkRemoteControlFiles()
			if err != nil {
				log.Printf("Failed to check remote control files: %v", err)
			} else if controlFile != nil {
				// 处理远程控制命令
				shouldExit, err := processRemoteControl(controlFile)
				if err != nil {
					log.Printf("Failed to process remote control: %v", err)
				}
				if shouldExit {
					log.Println("Exiting daemon mode due to remote control command")
					os.Exit(0)
				}
			}
		}

		// 查找ftp目录下所有文件并找出最新的
		newLatestFile, err := findLatestFile()
		if err != nil {
			log.Printf("Failed to find latest file: %v", err)
			// 没有找到最新的文件，大概是网络问题，需要用户介入
			// 如果没有文件，那么也就是没有频繁传递文件，不需要后台模式，先退了，等用户上传了再开启
			os.Exit(1)
		} else {
			// 比较最新文件和上一次处理的文件是否相同
			if newLatestFile != latestFile {
				log.Printf("New file detected: %s (previous: %s)", newLatestFile, latestFile)
				log.Println("")
				// 执行核心业务逻辑
				latestFile, processErr = executeDownloadProcess(serverIP, remote_enable)
				if processErr != nil {
					log.Printf("Failed to process file: %v", processErr)
					os.Exit(1)
				} else {
					log.Printf("Successfully processed new file: %s", latestFile)

					// Windows 10 toast notification (daemon mode only)
					if daemon_enable && isWindows() {
						importedToast := false
						// 动态导入 go-toast 库
						// 直接使用 go-toast API
						func() {
							// 仅在 Windows 下编译和运行
							// 需要在文件顶部 import "github.com/go-toast/toast"
							// 这里用匿名函数避免编译错误
							// 通知内容
							iconPath := ""
							if _, err := os.Stat("C:/Windows/System32/shell32.dll"); err == nil {
								iconPath = "C:/Windows/System32/shell32.dll"
							}
							notification := toast.Notification{
								AppID:   "Download Script",
								Title:   "File Processed Successfully",
								Message: fmt.Sprintf("File: %s\nTime: %s", latestFile, time.Now().Format("2006-01-02 15:04:05")),
								Icon:    iconPath,
							}
							err := notification.Push()
							if err != nil {
								log.Printf("Failed to send toast notification: %v", err)
							}
							importedToast = true
						}()
						if !importedToast {
							log.Printf("Toast notification not sent: go-toast not available or not on Windows platform")
						}
					}

					log.Println("Waiting for next check...")
				}
			} else {
				// 刷新当前行 - 使用ANSI转义序列清除当前行并重新定位光标
				fmt.Print("\033[2K\r")
				fmt.Printf("%s No new file detected. Checking again in 1 minute...", time.Now().Format("2006/01/02 15:04:05"))
			}
		}
		if !daemon_enable {
			// 显示处理结果
			if latestFile != "" {
				log.Printf("Latest file processed: %s", latestFile)
			} else {
				log.Println("No file was found or processed")
			}

			if processErr == nil {
				log.Println("Process completed successfully")
			} else {
				log.Println("Process failed")
				log.Printf("Error details: %v", processErr)
				os.Exit(1)
			}
			os.Exit(0)
		} else {

		}
		// 定时间隔
		time.Sleep(1 * time.Minute)
	}
}

// 使用winscp下载最新文件并更新到服务器上
func main() {
	// Load config
	err := loadConfig("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	// 执行Cobra命令
	if err := rootCmd.Execute(); err != nil {
		log.Printf("Error: %v", err)
		os.Exit(1)
	}
}

// 判断是否为 Windows 平台
func isWindows() bool {
	return strings.Contains(strings.ToLower(runtime.GOOS), "windows")
}
